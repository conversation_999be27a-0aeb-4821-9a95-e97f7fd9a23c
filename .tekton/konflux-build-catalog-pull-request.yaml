apiVersion: tekton.dev/v1
kind: PipelineRun
metadata:
  annotations:
    build.appstudio.openshift.io/repo: https://github.com/stolostron/konflux-build-catalog?rev={{revision}}
    build.appstudio.redhat.com/commit_sha: '{{revision}}'
    build.appstudio.redhat.com/pull_request_number: '{{pull_request_number}}'
    build.appstudio.redhat.com/target_branch: '{{target_branch}}'
    pipelinesascode.tekton.dev/cancel-in-progress: "true"
    pipelinesascode.tekton.dev/max-keep-runs: "3"
    pipelinesascode.tekton.dev/on-cel-expression: event == "pull_request" && target_branch
      == "main"
  creationTimestamp: null
  labels:
    appstudio.openshift.io/application: konflux-build-catalog
    appstudio.openshift.io/component: konflux-build-catalog
    pipelines.appstudio.openshift.io/type: build
  name: konflux-build-catalog-on-pull-request
  namespace: zxue-tenant
spec:
  params:
  - name: git-url
    value: '{{source_url}}'
  - name: revision
    value: '{{revision}}'
  - name: output-image
    value: quay.io/redhat-user-workloads/zxue-tenant/konflux-build-catalog:on-pr-{{revision}}
  - name: image-expires-after
    value: 5d
  - name: dockerfile
    value: Dockerfile
  pipelineSpec:
    description: |
      This pipeline is ideal for building container images from a Containerfile while maintaining trust after pipeline customization.

      _Uses `buildah` to create a container image leveraging [trusted artifacts](https://konflux-ci.dev/architecture/ADR/0036-trusted-artifacts.html). It also optionally creates a source image and runs some build-time tests. Information is shared between tasks using OCI artifacts instead of PVCs. EC will pass the [`trusted_task.trusted`](https://enterprisecontract.dev/docs/ec-policies/release_policy.html#trusted_task__trusted) policy as long as all data used to build the artifact is generated from trusted tasks.
      This pipeline is pushed as a Tekton bundle to [quay.io](https://quay.io/repository/konflux-ci/tekton-catalog/pipeline-docker-build-oci-ta?tab=tags)_
    finally:
    - name: show-sbom
      params:
      - name: IMAGE_URL
        value: $(tasks.build-image-index.results.IMAGE_URL)
      taskRef:
        params:
        - name: name
          value: show-sbom
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-show-sbom:0.1@sha256:002f7c8c1d2f9e09904035da414aba1188ae091df0ea9532cd997be05e73d594
        - name: kind
          value: task
        resolver: bundles
    params:
    - description: Source Repository URL
      name: git-url
      type: string
    - default: ""
      description: Revision of the Source Repository
      name: revision
      type: string
    - description: Fully Qualified Output Image
      name: output-image
      type: string
    - default: .
      description: Path to the source code of an application's component from where
        to build image.
      name: path-context
      type: string
    - default: Dockerfile
      description: Path to the Dockerfile inside the context specified by parameter
        path-context
      name: dockerfile
      type: string
    - default: "false"
      description: Force rebuild image
      name: rebuild
      type: string
    - default: "false"
      description: Skip checks against built image
      name: skip-checks
      type: string
    - default: "false"
      description: Execute the build with network isolation
      name: hermetic
      type: string
    - default: ""
      description: Build dependencies to be prefetched by Cachi2
      name: prefetch-input
      type: string
    - default: ""
      description: Image tag expiration time, time values could be something like
        1h, 2d, 3w for hours, days, and weeks, respectively.
      name: image-expires-after
    - default: "false"
      description: Build a source image.
      name: build-source-image
      type: string
    - default: "false"
      description: Add built image into an OCI image index
      name: build-image-index
      type: string
    - default: []
      description: Array of --build-arg values ("arg=value" strings) for buildah
      name: build-args
      type: array
    - default: ""
      description: Path to a file with build arguments for buildah, see https://www.mankier.com/1/buildah-build#--build-arg-file
      name: build-args-file
      type: string
    - default: "false"
      description: Whether to enable privileged mode, should be used only with remote
        VMs
      name: privileged-nested
      type: string
    results:
    - description: ""
      name: IMAGE_URL
      value: $(tasks.build-image-index.results.IMAGE_URL)
    - description: ""
      name: IMAGE_DIGEST
      value: $(tasks.build-image-index.results.IMAGE_DIGEST)
    - description: ""
      name: CHAINS-GIT_URL
      value: $(tasks.clone-repository.results.url)
    - description: ""
      name: CHAINS-GIT_COMMIT
      value: $(tasks.clone-repository.results.commit)
    tasks:
    - name: init
      params:
      - name: image-url
        value: $(params.output-image)
      - name: rebuild
        value: $(params.rebuild)
      - name: skip-checks
        value: $(params.skip-checks)
      taskRef:
        params:
        - name: name
          value: init
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-init:0.2@sha256:66e90d31e1386bf516fb548cd3e3f0082b5d0234b8b90dbf9e0d4684b70dbe1a
        - name: kind
          value: task
        resolver: bundles
    - name: clone-repository
      params:
      - name: url
        value: $(params.git-url)
      - name: revision
        value: $(params.revision)
      - name: ociStorage
        value: $(params.output-image).git
      - name: ociArtifactExpiresAfter
        value: $(params.image-expires-after)
      runAfter:
      - init
      taskRef:
        params:
        - name: name
          value: git-clone-oci-ta
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-git-clone-oci-ta:0.1@sha256:0fea1e4bd2fdde46c5b7786629f423a51e357f681c32ceddd744a6e3d48b8327
        - name: kind
          value: task
        resolver: bundles
      when:
      - input: $(tasks.init.results.build)
        operator: in
        values:
        - "true"
      workspaces:
      - name: basic-auth
        workspace: git-auth
    - name: prefetch-dependencies
      params:
      - name: input
        value: $(params.prefetch-input)
      - name: SOURCE_ARTIFACT
        value: $(tasks.clone-repository.results.SOURCE_ARTIFACT)
      - name: ociStorage
        value: $(params.output-image).prefetch
      - name: ociArtifactExpiresAfter
        value: $(params.image-expires-after)
      runAfter:
      - clone-repository
      taskRef:
        params:
        - name: name
          value: prefetch-dependencies-oci-ta
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-prefetch-dependencies-oci-ta:0.2@sha256:adbd819c6b727ac0c5519475d174dcad64cfa8df6ee50acd58f7fb562c59d4f7
        - name: kind
          value: task
        resolver: bundles
      workspaces:
      - name: git-basic-auth
        workspace: git-auth
      - name: netrc
        workspace: netrc
    - name: build-container
      params:
      - name: IMAGE
        value: $(params.output-image)
      - name: DOCKERFILE
        value: $(params.dockerfile)
      - name: CONTEXT
        value: $(params.path-context)
      - name: HERMETIC
        value: $(params.hermetic)
      - name: PREFETCH_INPUT
        value: $(params.prefetch-input)
      - name: IMAGE_EXPIRES_AFTER
        value: $(params.image-expires-after)
      - name: COMMIT_SHA
        value: $(tasks.clone-repository.results.commit)
      - name: BUILD_ARGS
        value:
        - $(params.build-args[*])
      - name: BUILD_ARGS_FILE
        value: $(params.build-args-file)
      - name: PRIVILEGED_NESTED
        value: $(params.privileged-nested)
      - name: SOURCE_ARTIFACT
        value: $(tasks.prefetch-dependencies.results.SOURCE_ARTIFACT)
      - name: CACHI2_ARTIFACT
        value: $(tasks.prefetch-dependencies.results.CACHI2_ARTIFACT)
      runAfter:
      - prefetch-dependencies
      taskRef:
        params:
        - name: name
          value: buildah-oci-ta
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-buildah-oci-ta:0.4@sha256:09f012a6c726c66922703f28846a3cfa196e8a391729192cda0d8f8a757b6ff5
        - name: kind
          value: task
        resolver: bundles
      when:
      - input: $(tasks.init.results.build)
        operator: in
        values:
        - "true"
    - name: build-image-index
      params:
      - name: IMAGE
        value: $(params.output-image)
      - name: COMMIT_SHA
        value: $(tasks.clone-repository.results.commit)
      - name: IMAGE_EXPIRES_AFTER
        value: $(params.image-expires-after)
      - name: ALWAYS_BUILD_INDEX
        value: $(params.build-image-index)
      - name: IMAGES
        value:
        - $(tasks.build-container.results.IMAGE_URL)@$(tasks.build-container.results.IMAGE_DIGEST)
      runAfter:
      - build-container
      taskRef:
        params:
        - name: name
          value: build-image-index
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-build-image-index:0.1@sha256:9c95b1fe17db091ae364344ba2006af46648e08486eef1f6fe1b9e3f10866875
        - name: kind
          value: task
        resolver: bundles
      when:
      - input: $(tasks.init.results.build)
        operator: in
        values:
        - "true"
    - name: build-source-image
      params:
      - name: BINARY_IMAGE
        value: $(params.output-image)
      - name: SOURCE_ARTIFACT
        value: $(tasks.prefetch-dependencies.results.SOURCE_ARTIFACT)
      - name: CACHI2_ARTIFACT
        value: $(tasks.prefetch-dependencies.results.CACHI2_ARTIFACT)
      runAfter:
      - build-image-index
      taskRef:
        params:
        - name: name
          value: source-build-oci-ta
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-source-build-oci-ta:0.2@sha256:c5e56643c0f5e19409e86c8fd4de4348413b6f10456aa0875498d5c63bf6ef0e
        - name: kind
          value: task
        resolver: bundles
      when:
      - input: $(tasks.init.results.build)
        operator: in
        values:
        - "true"
      - input: $(params.build-source-image)
        operator: in
        values:
        - "true"
    - name: deprecated-base-image-check
      params:
      - name: IMAGE_URL
        value: $(tasks.build-image-index.results.IMAGE_URL)
      - name: IMAGE_DIGEST
        value: $(tasks.build-image-index.results.IMAGE_DIGEST)
      runAfter:
      - build-image-index
      taskRef:
        params:
        - name: name
          value: deprecated-image-check
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-deprecated-image-check:0.5@sha256:ecd33669676b3a193ff4c2c6223cb912cc1b0cf5cc36e080eaec7718500272cf
        - name: kind
          value: task
        resolver: bundles
      when:
      - input: $(params.skip-checks)
        operator: in
        values:
        - "false"
    - name: clair-scan
      params:
      - name: image-digest
        value: $(tasks.build-image-index.results.IMAGE_DIGEST)
      - name: image-url
        value: $(tasks.build-image-index.results.IMAGE_URL)
      runAfter:
      - build-image-index
      taskRef:
        params:
        - name: name
          value: clair-scan
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-clair-scan:0.2@sha256:68a8fe28527c4469243119a449e2b3a6655f2acac589c069ea6433242da8ed4d
        - name: kind
          value: task
        resolver: bundles
      when:
      - input: $(params.skip-checks)
        operator: in
        values:
        - "false"
    - name: ecosystem-cert-preflight-checks
      params:
      - name: image-url
        value: $(tasks.build-image-index.results.IMAGE_URL)
      runAfter:
      - build-image-index
      taskRef:
        params:
        - name: name
          value: ecosystem-cert-preflight-checks
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-ecosystem-cert-preflight-checks:0.2@sha256:8a2d3ce9205df1f59f410529cb38134336e0a4b06ee1187b3229f26c80ecc5ba
        - name: kind
          value: task
        resolver: bundles
      when:
      - input: $(params.skip-checks)
        operator: in
        values:
        - "false"
    - name: sast-snyk-check
      params:
      - name: image-digest
        value: $(tasks.build-image-index.results.IMAGE_DIGEST)
      - name: image-url
        value: $(tasks.build-image-index.results.IMAGE_URL)
      - name: SOURCE_ARTIFACT
        value: $(tasks.prefetch-dependencies.results.SOURCE_ARTIFACT)
      - name: CACHI2_ARTIFACT
        value: $(tasks.prefetch-dependencies.results.CACHI2_ARTIFACT)
      runAfter:
      - build-image-index
      taskRef:
        params:
        - name: name
          value: sast-snyk-check-oci-ta
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-sast-snyk-check-oci-ta:0.4@sha256:9a6ec5575f80668552d861e64414e736c85af772c272ca653a6fd1ec841d2627
        - name: kind
          value: task
        resolver: bundles
      when:
      - input: $(params.skip-checks)
        operator: in
        values:
        - "false"
    - name: clamav-scan
      params:
      - name: image-digest
        value: $(tasks.build-image-index.results.IMAGE_DIGEST)
      - name: image-url
        value: $(tasks.build-image-index.results.IMAGE_URL)
      runAfter:
      - build-image-index
      taskRef:
        params:
        - name: name
          value: clamav-scan
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-clamav-scan:0.2@sha256:386c8c3395b44f6eb927dbad72382808b0ae42008f183064ca77cb4cad998442
        - name: kind
          value: task
        resolver: bundles
      when:
      - input: $(params.skip-checks)
        operator: in
        values:
        - "false"
    - name: sast-coverity-check
      params:
      - name: image-digest
        value: $(tasks.build-image-index.results.IMAGE_DIGEST)
      - name: image-url
        value: $(tasks.build-image-index.results.IMAGE_URL)
      - name: IMAGE
        value: $(params.output-image)
      - name: DOCKERFILE
        value: $(params.dockerfile)
      - name: CONTEXT
        value: $(params.path-context)
      - name: HERMETIC
        value: $(params.hermetic)
      - name: PREFETCH_INPUT
        value: $(params.prefetch-input)
      - name: IMAGE_EXPIRES_AFTER
        value: $(params.image-expires-after)
      - name: COMMIT_SHA
        value: $(tasks.clone-repository.results.commit)
      - name: BUILD_ARGS
        value:
        - $(params.build-args[*])
      - name: BUILD_ARGS_FILE
        value: $(params.build-args-file)
      - name: SOURCE_ARTIFACT
        value: $(tasks.prefetch-dependencies.results.SOURCE_ARTIFACT)
      - name: CACHI2_ARTIFACT
        value: $(tasks.prefetch-dependencies.results.CACHI2_ARTIFACT)
      runAfter:
      - coverity-availability-check
      taskRef:
        params:
        - name: name
          value: sast-coverity-check-oci-ta
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-sast-coverity-check-oci-ta:0.3@sha256:7c845b10d257b874f645ea30deeff3c1ce2b38e7b6e331564f32c8684f41b520
        - name: kind
          value: task
        resolver: bundles
      when:
      - input: $(params.skip-checks)
        operator: in
        values:
        - "false"
      - input: $(tasks.coverity-availability-check.results.STATUS)
        operator: in
        values:
        - success
    - name: coverity-availability-check
      runAfter:
      - build-image-index
      taskRef:
        params:
        - name: name
          value: coverity-availability-check
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-coverity-availability-check:0.2@sha256:8b58c4fae00c0dfe3937abfb8a9a61aa3c408cca4278b817db53d518428d944e
        - name: kind
          value: task
        resolver: bundles
      when:
      - input: $(params.skip-checks)
        operator: in
        values:
        - "false"
    - name: sast-shell-check
      params:
      - name: image-digest
        value: $(tasks.build-image-index.results.IMAGE_DIGEST)
      - name: image-url
        value: $(tasks.build-image-index.results.IMAGE_URL)
      - name: SOURCE_ARTIFACT
        value: $(tasks.prefetch-dependencies.results.SOURCE_ARTIFACT)
      - name: CACHI2_ARTIFACT
        value: $(tasks.prefetch-dependencies.results.CACHI2_ARTIFACT)
      runAfter:
      - build-image-index
      taskRef:
        params:
        - name: name
          value: sast-shell-check-oci-ta
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-sast-shell-check-oci-ta:0.1@sha256:60a7ee6ec5d00920389f03befd328cdaa159b7122a94ff3c87da287e0f32420f
        - name: kind
          value: task
        resolver: bundles
      when:
      - input: $(params.skip-checks)
        operator: in
        values:
        - "false"
    - name: sast-unicode-check
      params:
      - name: image-digest
        value: $(tasks.build-image-index.results.IMAGE_DIGEST)
      - name: image-url
        value: $(tasks.build-image-index.results.IMAGE_URL)
      - name: SOURCE_ARTIFACT
        value: $(tasks.prefetch-dependencies.results.SOURCE_ARTIFACT)
      - name: CACHI2_ARTIFACT
        value: $(tasks.prefetch-dependencies.results.CACHI2_ARTIFACT)
      runAfter:
      - build-image-index
      taskRef:
        params:
        - name: name
          value: sast-unicode-check-oci-ta
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-sast-unicode-check-oci-ta:0.2@sha256:9613b9037e4199495800c2054c13d0479e3335ec94e0f15f031a5bce844003a9
        - name: kind
          value: task
        resolver: bundles
      when:
      - input: $(params.skip-checks)
        operator: in
        values:
        - "false"
    - name: apply-tags
      params:
      - name: IMAGE_URL
        value: $(tasks.build-image-index.results.IMAGE_URL)
      - name: IMAGE_DIGEST
        value: $(tasks.build-image-index.results.IMAGE_DIGEST)
      runAfter:
      - build-image-index
      taskRef:
        params:
        - name: name
          value: apply-tags
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-apply-tags:0.2@sha256:0c411c27483849a936c0c420a57e477113e9fafc63077647200d6614d9ebb872
        - name: kind
          value: task
        resolver: bundles
    - name: push-dockerfile
      params:
      - name: IMAGE
        value: $(tasks.build-image-index.results.IMAGE_URL)
      - name: IMAGE_DIGEST
        value: $(tasks.build-image-index.results.IMAGE_DIGEST)
      - name: DOCKERFILE
        value: $(params.dockerfile)
      - name: CONTEXT
        value: $(params.path-context)
      - name: SOURCE_ARTIFACT
        value: $(tasks.prefetch-dependencies.results.SOURCE_ARTIFACT)
      runAfter:
      - build-image-index
      taskRef:
        params:
        - name: name
          value: push-dockerfile-oci-ta
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-push-dockerfile-oci-ta:0.1@sha256:d0ee13ab3d9564f7ee806a8ceaced934db493a3a40e11ff6db3a912b8bbace95
        - name: kind
          value: task
        resolver: bundles
    - name: rpms-signature-scan
      params:
      - name: image-url
        value: $(tasks.build-image-index.results.IMAGE_URL)
      - name: image-digest
        value: $(tasks.build-image-index.results.IMAGE_DIGEST)
      runAfter:
      - build-image-index
      taskRef:
        params:
        - name: name
          value: rpms-signature-scan
        - name: bundle
          value: quay.io/konflux-ci/tekton-catalog/task-rpms-signature-scan:0.2@sha256:ec7f6de651458e4a5842b145e761b0d86b03b52bec1515d6d8a1b8cf107af95c
        - name: kind
          value: task
        resolver: bundles
      when:
      - input: $(params.skip-checks)
        operator: in
        values:
        - "false"
    workspaces:
    - name: git-auth
      optional: true
    - name: netrc
      optional: true
  taskRunTemplate:
    serviceAccountName: build-pipeline-konflux-build-catalog
  workspaces:
  - name: git-auth
    secret:
      secretName: '{{ git_auth_secret }}'
status: {}
