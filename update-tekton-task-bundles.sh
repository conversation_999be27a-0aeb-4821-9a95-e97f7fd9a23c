#!/bin/bash

# Use this script to update the Tekton Task Bundle references used in a Pipeline or a PipelineRun.
# update-tekton-task-bundles.sh .tekton/*.yaml

set -euo pipefail

FILES=$@

# Find existing image references
OLD_REFS="$(\
    yq '... | select(has("resolver")) | .params // [] | .[] | select(.name == "bundle") | .value'  $FILES | \
    grep -v -- '---' | \
    sort -u \
)"

# Find updates for image references
for old_ref in ${OLD_REFS}; do
    repo_tag="${old_ref%@*}"
    repo="${repo_tag%:*}"
    old_tag="${repo_tag##*:}"

    tags=$(skopeo list-tags docker://${repo} | yq '.Tags[]' | tr -d '"')

    main_tags=$(echo "$tags" | grep -E '^[0-9]+(\.[0-9]+)*$')
    latest_main_tag=$(echo "$main_tags" | sort -V | tail -n1)

    if [[ "$old_tag" != "$latest_main_tag" ]]; then
        echo "WARNING: $repo has a newer main tag: $old_tag -> $latest_main_tag"
        echo "         Please review the migration guide or changelog before upgrading."
        task_name=$(basename $repo)
        task_name=${task_name#task-}
        echo "         See: https://github.com/konflux-ci/build-definitions/tree/main/task/${task_name}"
        continue
    fi

    new_digest=$(skopeo inspect docker://${repo}:${old_tag} | yq '.Digest')
    new_ref="${repo}:${old_tag}@${new_digest}"
    for file in $FILES; do
        sed -i '' -e "s!${old_ref}!${new_ref}!g" $file
    done
done
